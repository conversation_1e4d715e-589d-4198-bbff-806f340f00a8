# 05: RBAC System User Guide (v3.0)

## **Table of Contents**

1. [Introduction to RBAC](#introduction-to-rbac)
2. [Understanding Your Access](#understanding-your-access)
3. [Current System Capabilities](#current-system-capabilities)
4. [Administrator Guide](#administrator-guide)
5. [System Architecture & Implementation](#system-architecture--implementation)
6. [Troubleshooting & FAQ](#troubleshooting--faq)

---

## **Introduction to RBAC**

### **What is RBAC?**

Role-Based Access Control (RBAC) is a security system that controls what you can do in the application based on your **role** and **permissions**. The current implementation uses Django's built-in Group system enhanced with custom models for staff management.

### **Key Concepts**

#### **Roles (Groups)**

Roles are implemented as Django Groups with predefined names and permissions. Current available roles include:

- **Staff Manager (SM)**: Complete staff and system management
- **Department Head (DH)**: Department-level staff management
- **HR Administrator (HRA)**: Staff profile and user management
- **Product Management Executive (PME)**: Product catalog management (planned)
- **Order Management Executive (OME)**: Order processing management (planned)
- **Customer Service Representative (CSR)**: Customer support operations (planned)

#### **Permissions**

Permissions are Django's built-in permission system plus custom permissions. Examples:

- `auth.add_user` - Create new users
- `auth.view_group` - View groups/roles
- `core.can_toggle_staff_status` - Toggle user staff status
- `core.can_manage_staff_roles` - Manage staff role assignments
- `core.can_view_audit_logs` - Access audit logs

#### **How They Work Together**

Users are assigned to Groups (Roles), and Groups have specific Permissions. The system checks permissions dynamically for each action.

### **System Architecture**

```
User → Assigned to Group(s) → Group has Permissions → Permissions allow API Access
```

**Example**: Sarah (User) → Staff Manager (Group) → Can manage users/groups (Permissions) → Access staff management APIs (Actions)

---

## **Understanding Your Access**

### **Checking Your Current Role and Permissions**

#### **Via API**

```bash
GET /api/staff/auth/me/
```

**Response Example**:

```json
{
  "user": {
    "email": "<EMAIL>",
    "roles": ["Product Management Executive (PME)"],
    "permissions": [
      "products.add_product",
      "products.change_product",
      "products.view_product"
    ],
    "staff_profile": {
      "employee_id": "EMP001",
      "department": "PRODUCT",
      "position_title": "Senior Product Manager"
    }
  }
}
```

### **Understanding Permission Names**

Permission names follow the pattern: `app.action_model`

**Examples**:

- `products.add_product` = Can create products
- `orders.change_order` = Can modify orders
- `customers.view_customer` = Can view customer information
- `core.can_toggle_staff_status` = Can promote users to staff

### **Role Hierarchy Overview**

#### **Staff Management Roles**

- **Staff Manager (SM)**: Complete staff management authority
- **Department Head (DH)**: Manages team within department
- **HR Administrator (HRA)**: Handles staff onboarding and profiles

#### **Business Function Roles**

- **Product Management Executive (PME)**: Full product management
- **Product Team Member (PMGM)**: Limited product operations
- **Order Management Executive (OME)**: Complete order management
- **Customer Service Representative (CSR)**: Customer support operations

---

## **Common User Actions**

### **For All Staff Users**

#### **1. View Your Profile**

```bash
GET /api/staff/auth/me/
```

Shows your roles, permissions, and staff profile information.

#### **2. Check What You Can Do**

```bash
GET /api/staff/auth/permissions/
```

Lists all permissions available in the system with descriptions.

#### **3. View Available Roles**

```bash
GET /api/staff/roles/
```

See all roles in the system and their member counts.

### **For Product Team Members**

#### **1. View Products**

```bash
GET /api/staff/products/
```

#### **2. Create New Product** (if you have permission)

```bash
POST /api/staff/products/
{
  "name": "New Product",
  "category": 1,
  "price": "99.99"
}
```

#### **3. Check Product Permissions**

```bash
GET /api/staff/roles/{role_id}/permissions/
```

### **For Order Management Team**

#### **1. View Orders**

```bash
GET /api/staff/orders/
```

#### **2. Update Order Status** (if you have permission)

```bash
PATCH /api/staff/orders/{order_id}/
{
  "status": "PROCESSING"
}
```

### **For Customer Service**

#### **1. View Customer Information**

```bash
GET /api/staff/customers/
```

#### **2. View Customer Orders**

```bash
GET /api/staff/customers/{customer_id}/orders/
```

---

## **Administrator Guide**

### **Staff Managers: Complete Staff Management**

#### **1. Create New Staff User**

```bash
POST /api/staff/staff-management/create_staff_user/
{
  "email": "<EMAIL>",
  "employee_id": "EMP123",
  "department": "PRODUCT",
  "position_title": "Product Specialist",
  "manager_id": 5,
  "hire_date": "2024-01-15",
  "role_ids": [3, 4],
  "notes": "Experienced hire from competitor"
}
```

#### **2. View All Staff Profiles**

```bash
GET /api/staff/staff-profiles/
```

**Filter by department**:

```bash
GET /api/staff/staff-profiles/?department=PRODUCT
```

**Search staff**:

```bash
GET /api/staff/staff-profiles/?search=john
```

#### **3. Manage Staff Status**

```bash
POST /api/staff/staff-profiles/{profile_id}/change_status/
{
  "status": "INACTIVE"
}
```

**Status Options**:

- `ACTIVE`: Working normally
- `INACTIVE`: Temporarily disabled
- `ON_LEAVE`: On leave but still employed
- `TERMINATED`: No longer with company

#### **4. View Department Summary**

```bash
GET /api/staff/staff-profiles/department_summary/
```

**Response**:

```json
{
  "department_summary": {
    "PRODUCT": {
      "name": "Product Management",
      "total_staff": 8,
      "managers": 2
    },
    "ORDER": {
      "name": "Order Management", 
      "total_staff": 5,
      "managers": 1
    }
  }
}
```

### **Role Management**

#### **1. Create New Role**

```bash
POST /api/staff/roles/
{
  "name": "Inventory Specialist (IS)",
  "permission_ids": [15, 16, 17]
}
```

#### **2. Add Permission to Role**

```bash
POST /api/staff/roles/{role_id}/add_permission/
{
  "permission_codename": "products.add_product"
}
```

#### **3. Remove Permission from Role**

```bash
POST /api/staff/roles/{role_id}/remove_permission/
{
  "permission_codename": "products.delete_product"
}
```

#### **4. View Role Members**

```bash
GET /api/staff/roles/{role_id}/members/
```

#### **5. Assign User to Role**

```bash
POST /api/staff/groups/{group_id}/add_member/
{
  "user_id": 123,
  "notes": "Promoted to team lead"
}
```

### **User Management**

#### **1. View All Users**

```bash
GET /api/staff/users/
```

#### **2. Toggle Staff Status** (Staff Managers only)

```bash
POST /api/staff/users/{user_id}/toggle_staff/
```

#### **3. View User Details**

```bash
GET /api/staff/users/{user_id}/
```

Shows complete user information including roles, permissions, and staff profile.

### **Audit and Monitoring**

#### **1. View Audit Logs**

```bash
GET /api/staff/audit/
```

**Filter by action**:

```bash
GET /api/staff/audit/?action=staff_user_created
```

**Filter by date range**:

```bash
GET /api/staff/audit/?start_date=2024-01-01&end_date=2024-01-31
```

#### **2. View Audit Summary**

```bash
GET /api/staff/audit/summary/
```

Shows activity breakdown by action type and user.

---

## **System Flexibility & Possibilities**

### **What Makes This System Flexible?**

#### **1. Permission-Based Architecture**

- **No Hardcoded Restrictions**: Permissions are checked dynamically
- **Easy Role Modifications**: Change role permissions without code changes
- **Granular Control**: Assign specific permissions to specific roles

#### **2. Organizational Hierarchy**

- **Manager-Subordinate Relationships**: Clear reporting structure
- **Department-Based Access**: Filter access by department
- **Circular Dependency Prevention**: System prevents invalid management chains

#### **3. Custom Permissions**

- **Specialized Operations**: Custom permissions for unique business needs
- **Delegated Authority**: Distribute superuser capabilities to appropriate roles
- **Audit Control**: Granular access to audit logs and system monitoring

### **Possibilities for Customization**

#### **1. Create New Roles**

```bash
# Example: Create a "Quality Assurance" role
POST /api/staff/roles/
{
  "name": "Quality Assurance Specialist (QAS)",
  "permission_ids": [
    # View products
    products.view_product,
    # View orders for quality checks
    orders.view_order,
    # Custom QA permissions (if created)
    core.can_approve_products
  ]
}
```

#### **2. Department-Specific Permissions**

- Create roles specific to departments
- Assign department heads with limited scope
- Cross-department collaboration roles

#### **3. Temporary Access**

- Create temporary roles for contractors
- Time-limited permissions for special projects
- Emergency access roles for critical situations

#### **4. Integration Possibilities**

- **External Systems**: API endpoints for third-party integrations
- **Reporting Tools**: Export user and permission data
- **Automation**: Webhook support for permission changes

### **Best Practices**

#### **1. Role Design**

- **Principle of Least Privilege**: Give minimum necessary permissions
- **Business-Focused Names**: Use clear, descriptive role names
- **Regular Review**: Periodically review and update role permissions

#### **2. Staff Management**

- **Clear Hierarchy**: Maintain clear manager-subordinate relationships
- **Documentation**: Use notes fields for important information
- **Status Management**: Keep staff status updated

#### **3. Security**

- **Regular Audits**: Review audit logs regularly
- **Permission Reviews**: Quarterly permission audits
- **Access Monitoring**: Monitor for unusual access patterns

---

## **Troubleshooting & FAQ**

### **Common Issues**

#### **"Permission Denied" Errors**

**Problem**: User can't perform an action
**Solution**:

1. Check user's current permissions: `GET /api/staff/auth/me/`
2. Verify required permission for the action
3. Contact Staff Manager to assign appropriate role

#### **"Circular Management Dependency" Error**

**Problem**: Can't assign manager due to circular dependency
**Solution**:

1. Check management chain: Review who manages whom
2. Break the circle: Temporarily remove conflicting manager assignments
3. Reassign properly: Set up hierarchy without circular references

#### **Can't Create Staff User**

**Problem**: Staff creation fails
**Solution**:

1. Verify you have `staff.add_staffprofile` permission
2. Check if email already exists in system
3. Ensure employee_id is unique
4. Verify manager_id exists and is active

### **Frequently Asked Questions**

#### **Q: Can a user have multiple roles?**

A: Yes! Users can be assigned to multiple roles, and they'll have the combined permissions of all their roles.

#### **Q: How do I know what permissions a role has?**

A: Use `GET /api/staff/roles/{role_id}/permissions/` to see detailed permission breakdown.

#### **Q: Can I create custom permissions?**

A: Custom permissions need to be added to the Django models. Contact your development team for new permission requirements.

#### **Q: What happens when a staff member leaves?**

A: Change their status to "TERMINATED" which will deactivate their account while preserving audit history.

#### **Q: How do I delegate staff management to department heads?**

A: Assign the "Department Head (DH)" role which includes `core.can_manage_staff_roles` permission for their department.

#### **Q: Can I see who made changes to the system?**

A: Yes! All actions are logged in the audit system. Use `GET /api/staff/audit/` to view activity logs.

### **Getting Help**

#### **For Users**

- Contact your Department Head for role-related questions
- Contact Staff Manager for access issues
- Check audit logs to understand permission changes

#### **For Administrators**

- Review this guide for common procedures
- Check audit logs for troubleshooting
- Contact system administrators for technical issues

#### **For Developers**

- Refer to implementation documentation
- Check Django admin for low-level permission management
- Review audit logs for system behavior analysis

---

**System Version**: v2.0  
**Last Updated**: 2024  
**Contact**: System Administrator
